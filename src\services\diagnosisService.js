// Mock AI Analysis Service
// في التطبيق الحقيقي، ستحتاج إلى دمج APIs حقيقية مثل:
// - Microsoft Azure Custom Vision
// - Infermedica API
// - أو خدمات AI أخرى

const mockConditions = [
  {
    id: 'normal',
    name: 'عين سليمة',
    description: 'العين تبدو في حالة طبيعية وصحية. لا توجد علامات واضحة على وجود مشاكل.',
    severity: 'normal',
    treatment: [
      'الحفاظ على نظافة العين',
      'استخدام قطرات مرطبة عند الحاجة',
      'تجنب فرك العين',
      'ارتداء نظارات شمسية عند التعرض للشمس'
    ],
    needsDoctor: false
  },
  {
    id: 'conjunctivitis',
    name: 'التها<PERSON> الملتحمة',
    description: 'التهاب في الغشاء المخاطي الذي يغطي الجزء الأبيض من العين والجفن الداخلي.',
    severity: 'moderate',
    treatment: [
      'استخدام كمادات باردة',
      'تجنب لمس العين',
      'غسل اليدين بانتظام',
      'استخدام قطرات مضادة للالتهاب (بوصفة طبية)'
    ],
    needsDoctor: true
  },
  {
    id: 'dryeye',
    name: 'جفاف العين',
    description: 'نقص في إنتاج الدموع أو تبخرها بسرعة، مما يؤدي إلى جفاف سطح العين.',
    severity: 'mild',
    treatment: [
      'استخدام قطرات الدموع الاصطناعية',
      'تجنب التعرض للهواء الجاف',
      'أخذ فترات راحة من الشاشات',
      'شرب كمية كافية من الماء'
    ],
    needsDoctor: false
  },
  {
    id: 'redness',
    name: 'احمرار العين',
    description: 'توسع في الأوعية الدموية في العين مما يسبب ظهور اللون الأحمر.',
    severity: 'mild',
    treatment: [
      'استخدام كمادات باردة',
      'تجنب المهيجات',
      'استخدام قطرات مرطبة',
      'الحصول على راحة كافية'
    ],
    needsDoctor: false
  },
  {
    id: 'stye',
    name: 'شحاذ العين (الجليجل)',
    description: 'التهاب في الغدد الدهنية في الجفن يظهر كنتوء أحمر مؤلم.',
    severity: 'moderate',
    treatment: [
      'استخدام كمادات دافئة',
      'تجنب الضغط على المنطقة',
      'الحفاظ على نظافة العين',
      'استخدام مضاد حيوي موضعي (بوصفة طبية)'
    ],
    needsDoctor: true
  }
];

// محاكاة تحليل الصورة
const analyzeImage = async (imageFile) => {
  // محاكاة وقت المعالجة
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // في التطبيق الحقيقي، ستقوم بإرسال الصورة إلى API للتحليل
  // هنا نقوم بإرجاع نتيجة عشوائية للتجربة
  const randomCondition = mockConditions[Math.floor(Math.random() * mockConditions.length)];
  const confidence = Math.floor(Math.random() * 30) + 70; // 70-100%
  
  return {
    condition: randomCondition.name,
    description: randomCondition.description,
    severity: randomCondition.severity,
    confidence: confidence,
    treatment: randomCondition.treatment,
    needsDoctor: randomCondition.needsDoctor,
    timestamp: new Date().toISOString(),
    imageAnalysis: {
      quality: 'good',
      eyeDetected: true,
      clarity: 'high'
    }
  };
};

// محاكاة استشارة Infermedica API
const getInfermedicaDiagnosis = async (symptoms) => {
  // محاكاة وقت المعالجة
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // في التطبيق الحقيقي، ستقوم بإرسال الأعراض إلى Infermedica API
  return {
    diagnosis: 'تم التحليل بنجاح',
    recommendations: [
      'متابعة الأعراض',
      'استشارة طبيب العيون إذا استمرت الأعراض'
    ]
  };
};

// الدالة الرئيسية للتشخيص
export const performDiagnosis = async (imageFile, onProgress) => {
  try {
    // المرحلة 1: تحليل الصورة
    if (onProgress) onProgress('analyzing');
    const imageAnalysis = await analyzeImage(imageFile);
    
    // المرحلة 2: التشخيص
    if (onProgress) onProgress('diagnosing');
    const infermedicaResult = await getInfermedicaDiagnosis([imageAnalysis.condition]);
    
    // المرحلة 3: إعداد التوصيات
    if (onProgress) onProgress('treatment');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      ...imageAnalysis,
      infermedicaData: infermedicaResult,
      analysisId: generateAnalysisId()
    };
    
  } catch (error) {
    console.error('خطأ في التشخيص:', error);
    throw new Error('حدث خطأ أثناء تحليل الصورة. يرجى المحاولة مرة أخرى.');
  }
};

// توليد معرف فريد للتحليل
const generateAnalysisId = () => {
  return 'AOX_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// التحقق من جودة الصورة
export const validateImage = (file) => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  
  if (!file) {
    throw new Error('لم يتم اختيار صورة');
  }
  
  if (file.size > maxSize) {
    throw new Error('حجم الصورة كبير جداً. الحد الأقصى 10MB');
  }
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error('نوع الملف غير مدعوم. استخدم JPG أو PNG');
  }
  
  return true;
};
