import React from 'react';
import { 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  Download, 
  Share2,
  Eye,
  Clock,
  Stethoscope
} from 'lucide-react';

const DiagnosisResult = ({ result, onDownloadReport, onShare }) => {
  if (!result) return null;

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'normal':
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      case 'mild':
        return <Info className="w-6 h-6 text-blue-500" />;
      case 'moderate':
        return <AlertTriangle className="w-6 h-6 text-yellow-500" />;
      case 'severe':
        return <AlertTriangle className="w-6 h-6 text-red-500" />;
      default:
        return <Info className="w-6 h-6 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'normal':
        return 'border-green-200 bg-green-50';
      case 'mild':
        return 'border-blue-200 bg-blue-50';
      case 'moderate':
        return 'border-yellow-200 bg-yellow-50';
      case 'severe':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto fade-in">
      <div className={`result-card rounded-lg p-6 ${getSeverityColor(result.severity)}`}>
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 bg-white rounded-full shadow-sm">
            <Eye className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-800">نتيجة التحليل</h2>
            <p className="text-sm text-gray-600 flex items-center gap-1">
              <Clock className="w-4 h-4" />
              {new Date().toLocaleString('ar-EG')}
            </p>
          </div>
        </div>

        {/* Diagnosis */}
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-3">
            {getSeverityIcon(result.severity)}
            <h3 className="text-lg font-semibold text-gray-800">التشخيص</h3>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h4 className="font-bold text-gray-800 mb-2">{result.condition}</h4>
            <p className="text-gray-600 leading-relaxed">{result.description}</p>
          </div>
        </div>

        {/* Confidence Score */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">دقة التشخيص</span>
            <span className="text-sm font-bold text-gray-800">{result.confidence}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${result.confidence}%` }}
            ></div>
          </div>
        </div>

        {/* Treatment Recommendations */}
        {result.treatment && (
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-3">
              <Stethoscope className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-800">التوصيات العلاجية</h3>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <ul className="space-y-2">
                {result.treatment.map((item, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-blue-600 mt-1">•</span>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Warning */}
        {result.needsDoctor && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <h4 className="font-semibold text-red-800">تحذير مهم</h4>
            </div>
            <p className="text-red-700 text-sm">
              يُنصح بزيارة طبيب العيون في أقرب وقت ممكن للحصول على تشخيص دقيق وعلاج مناسب.
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={onDownloadReport}
            className="btn-primary text-white px-6 py-3 rounded-lg flex items-center gap-2 justify-center flex-1"
          >
            <Download className="w-4 h-4" />
            تحميل التقرير
          </button>
          <button
            onClick={onShare}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 justify-center flex-1 transition-colors"
          >
            <Share2 className="w-4 h-4" />
            مشاركة النتيجة
          </button>
        </div>

        {/* Disclaimer */}
        <div className="mt-6 p-3 bg-gray-100 rounded-lg">
          <p className="text-xs text-gray-600 text-center">
            <strong>إخلاء مسؤولية:</strong> هذا التشخيص أولي وللاستشارة فقط. 
            لا يغني عن زيارة الطبيب المختص للحصول على تشخيص دقيق وعلاج مناسب.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DiagnosisResult;
