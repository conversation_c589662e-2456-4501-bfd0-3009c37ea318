import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Camera, Upload, X, Image as ImageIcon } from 'lucide-react';

const ImageUpload = ({ onImageUpload, isLoading }) => {
  const [uploadedImage, setUploadedImage] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);

  const onDrop = useCallback((acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedImage(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      onImageUpload(file);
    }
  }, [onImageUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png']
    },
    multiple: false,
    disabled: isLoading
  });

  const handleCameraCapture = (event) => {
    const file = event.target.files[0];
    if (file) {
      setUploadedImage(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      onImageUpload(file);
    }
  };

  const removeImage = () => {
    setUploadedImage(null);
    setPreviewUrl(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {!previewUrl ? (
        <div
          {...getRootProps()}
          className={`upload-area p-8 rounded-lg text-center cursor-pointer transition-all duration-300 ${
            isDragActive ? 'dragover' : ''
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center space-y-4">
            <div className="p-4 bg-blue-100 rounded-full">
              <ImageIcon className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">
                ارفع صورة العين
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                اسحب الصورة هنا أو انقر للاختيار
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  type="button"
                  className="btn-primary text-white px-4 py-2 rounded-lg flex items-center gap-2 justify-center"
                  disabled={isLoading}
                >
                  <Upload className="w-4 h-4" />
                  اختر صورة
                </button>
                <label className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 justify-center cursor-pointer transition-colors">
                  <Camera className="w-4 h-4" />
                  التقط صورة
                  <input
                    type="file"
                    accept="image/*"
                    capture="environment"
                    onChange={handleCameraCapture}
                    className="hidden"
                    disabled={isLoading}
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="relative">
          <div className="bg-white rounded-lg p-4 card-shadow">
            <div className="relative">
              <img
                src={previewUrl}
                alt="صورة العين المرفوعة"
                className="w-full h-64 object-cover rounded-lg"
              />
              <button
                onClick={removeImage}
                className="absolute top-2 left-2 bg-red-500 hover:bg-red-600 text-white p-2 rounded-full transition-colors"
                disabled={isLoading}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600">
                {uploadedImage?.name || 'صورة العين'}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                {uploadedImage?.size ? `${(uploadedImage.size / 1024 / 1024).toFixed(2)} MB` : ''}
              </p>
            </div>
          </div>
        </div>
      )}
      
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500">
          الصيغ المدعومة: JPG, PNG, JPEG (حد أقصى 10MB)
        </p>
      </div>
    </div>
  );
};

export default ImageUpload;
