# 👁️ Aox Eye - تحليل ذكي للعين

موقع ذكي لتحليل صور العين باستخدام الذكاء الاصطناعي. يرفع المستخدم صورة لعينه، والموقع يقوم بتحليل الحالة، تشخيصها، ثم اقتراح علاج مناسب.

## 🎯 الهدف

- تقديم تشخيص أولي لمشاكل العين بناءً على صورة
- عرض علاج مبدئي أو توصية بزيارة الطبيب
- واجهة احترافية سريعة وسهلة للموبايل والويب

## ✨ الخصائص الأساسية

### 1. 📸 رفع صورة للعين
- دعم رفع الصور أو التصوير بالكاميرا
- صيغ مدعومة: `.jpg`, `.png`, `.jpeg`
- حد أقصى: 10MB

### 2. 🤖 تحليل صورة العين (تشخيص أولي)
- استخدام الذكاء الاصطناعي لتحليل الصور
- كشف حالات مثل:
  - التهاب العين (Conjunctivitis)
  - احمرار (Redness)
  - جفاف (Dryness)
  - شحاذ العين (Stye)
  - عين سليمة (Normal)

### 3. 🧠 التشخيص والعلاج
- تشخيص أولي بناءً على تحليل الصورة
- توصيات علاجية مناسبة
- تنبيه المستخدم في الحالات الحرجة

### 4. 📋 عرض النتيجة
- عرض اسم الحالة + وصف مختصر
- اقتراح علاج (قطرات، أدوية، راحة...)
- تنبيه إذا لزم زيارة طبيب
- نسبة دقة التشخيص

### 5. 📤 مشاركة / تحميل النتيجة
- تنزيل تقرير PDF
- مشاركة النتيجة على واتساب أو بريد

## 🛠️ التقنيات المستخدمة

- **Frontend**: React 19 + Vite
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **File Upload**: React Dropzone
- **PDF Generation**: jsPDF
- **AI Analysis**: Mock Service (قابل للتطوير مع Azure Custom Vision أو Roboflow)

## 🚀 التشغيل

### المتطلبات
- Node.js 18+
- npm أو yarn

### التثبيت
```bash
# استنساخ المشروع
git clone [repository-url]
cd aox-eye

# تثبيت المكتبات
npm install

# تشغيل الخادم المحلي
npm run dev
```

### البناء للإنتاج
```bash
npm run build
```

## 📁 هيكل المشروع

```
src/
├── components/
│   ├── ImageUpload.jsx      # مكون رفع الصور
│   ├── LoadingSpinner.jsx   # مكون التحميل
│   └── DiagnosisResult.jsx  # مكون عرض النتائج
├── services/
│   ├── diagnosisService.js  # خدمة التشخيص
│   └── reportService.js     # خدمة التقارير
├── App.jsx                  # المكون الرئيسي
├── index.css               # الأنماط الرئيسية
└── main.jsx               # نقطة الدخول
```

## 🔮 التطوير المستقبلي

### APIs حقيقية
- **Microsoft Azure Custom Vision**: لتحليل الصور
- **Infermedica API**: للتشخيص الطبي
- **Roboflow + YOLOv8**: لتدريب نماذج مخصصة

### ميزات إضافية
- حفظ التاريخ الطبي
- تذكيرات العلاج
- ربط مع أطباء العيون
- دعم لغات متعددة
- تطبيق موبايل

## ⚠️ إخلاء مسؤولية

هذا التطبيق يقدم تشخيصاً أولياً للاستشارة فقط ولا يغني عن زيارة الطبيب المختص. يُنصح بمراجعة طبيب العيون للحصول على تشخيص دقيق وعلاج مناسب.

## 📄 الترخيص

جميع الحقوق محفوظة © 2024 Aox Eye
