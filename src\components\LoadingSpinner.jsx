import React from 'react';
import { <PERSON>, <PERSON>, Stethoscope } from 'lucide-react';

const LoadingSpinner = ({ stage = 'analyzing' }) => {
  const stages = {
    analyzing: {
      icon: <Eye className="w-8 h-8 text-blue-600" />,
      title: 'جاري تحليل الصورة...',
      description: 'نقوم بفحص صورة العين باستخدام الذكاء الاصطناعي'
    },
    diagnosing: {
      icon: <Brain className="w-8 h-8 text-purple-600" />,
      title: 'جاري التشخيص...',
      description: 'نحلل النتائج ونقوم بالتشخيص الأولي'
    },
    treatment: {
      icon: <Stethoscope className="w-8 h-8 text-green-600" />,
      title: 'جاري إعداد التوصيات...',
      description: 'نحضر التوصيات العلاجية المناسبة'
    }
  };

  const currentStage = stages[stage] || stages.analyzing;

  return (
    <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg card-shadow max-w-md mx-auto">
      <div className="relative mb-6">
        {/* Outer spinning ring */}
        <div className="loading-spinner mx-auto"></div>
        
        {/* Inner icon */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-white rounded-full p-2 shadow-sm">
            {currentStage.icon}
          </div>
        </div>
      </div>

      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          {currentStage.title}
        </h3>
        <p className="text-sm text-gray-600 leading-relaxed">
          {currentStage.description}
        </p>
      </div>

      {/* Progress dots */}
      <div className="flex gap-2 mt-6">
        {Object.keys(stages).map((stageKey, index) => (
          <div
            key={stageKey}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              stageKey === stage 
                ? 'bg-blue-600 scale-125' 
                : Object.keys(stages).indexOf(stage) > index
                  ? 'bg-green-500'
                  : 'bg-gray-300'
            }`}
          />
        ))}
      </div>

      <div className="mt-4 text-xs text-gray-500 text-center">
        قد تستغرق العملية بضع ثوانٍ...
      </div>
    </div>
  );
};

export default LoadingSpinner;
