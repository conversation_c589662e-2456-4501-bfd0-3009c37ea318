import React, { useState } from 'react';
import { Eye, Brain, Shield, Users } from 'lucide-react';
import ImageUpload from './components/ImageUpload';
import LoadingSpinner from './components/LoadingSpinner';
import DiagnosisResult from './components/DiagnosisResult';
import { performDiagnosis, validateImage } from './services/diagnosisService';
import { generatePDFReport, shareResult } from './services/reportService';

function App() {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStage, setLoadingStage] = useState('analyzing');
  const [diagnosisResult, setDiagnosisResult] = useState(null);
  const [error, setError] = useState(null);
  const [uploadedImage, setUploadedImage] = useState(null);

  const handleImageUpload = async (imageFile) => {
    try {
      setError(null);
      setDiagnosisResult(null);

      // التحقق من صحة الصورة
      validateImage(imageFile);

      setUploadedImage(imageFile);
      setIsLoading(true);

      // تنفيذ التشخيص
      const result = await performDiagnosis(imageFile, setLoadingStage);

      setDiagnosisResult(result);
      setIsLoading(false);

    } catch (err) {
      setError(err.message);
      setIsLoading(false);
    }
  };

  const handleDownloadReport = async () => {
    try {
      await generatePDFReport(diagnosisResult, uploadedImage);
    } catch (err) {
      alert('حدث خطأ أثناء تحميل التقرير: ' + err.message);
    }
  };

  const handleShare = async () => {
    try {
      await shareResult(diagnosisResult);
    } catch (err) {
      alert('حدث خطأ أثناء المشاركة: ' + err.message);
    }
  };

  const resetAnalysis = () => {
    setDiagnosisResult(null);
    setUploadedImage(null);
    setError(null);
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Eye className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Aox Eye</h1>
                <p className="text-sm text-gray-600">تحليل ذكي للعين بالذكاء الاصطناعي</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        {!isLoading && !diagnosisResult && (
          <div className="text-center mb-12">
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-600 rounded-full mb-4">
                <Eye className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                تحليل ذكي لصور العين
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                ارفع صورة لعينك واحصل على تشخيص أولي وتوصيات علاجية باستخدام أحدث تقنيات الذكاء الاصطناعي
              </p>
            </div>

            {/* Features */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white p-6 rounded-lg card-shadow">
                <Brain className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-800 mb-2">ذكاء اصطناعي متقدم</h3>
                <p className="text-sm text-gray-600">تحليل دقيق باستخدام أحدث تقنيات الذكاء الاصطناعي</p>
              </div>
              <div className="bg-white p-6 rounded-lg card-shadow">
                <Shield className="w-8 h-8 text-green-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-800 mb-2">آمن ومحمي</h3>
                <p className="text-sm text-gray-600">حماية كاملة لخصوصيتك وبياناتك الطبية</p>
              </div>
              <div className="bg-white p-6 rounded-lg card-shadow">
                <Users className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-800 mb-2">موثوق طبياً</h3>
                <p className="text-sm text-gray-600">مطور بالتعاون مع أطباء العيون المختصين</p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-500 rounded-full"></div>
              <span className="text-red-800 font-medium">خطأ</span>
            </div>
            <p className="text-red-700 mt-1">{error}</p>
            <button
              onClick={resetAnalysis}
              className="mt-3 text-red-600 hover:text-red-800 text-sm underline"
            >
              المحاولة مرة أخرى
            </button>
          </div>
        )}

        {/* Upload Section */}
        {!isLoading && !diagnosisResult && !error && (
          <ImageUpload onImageUpload={handleImageUpload} isLoading={isLoading} />
        )}

        {/* Loading Section */}
        {isLoading && (
          <LoadingSpinner stage={loadingStage} />
        )}

        {/* Results Section */}
        {diagnosisResult && !isLoading && (
          <div className="space-y-6">
            <DiagnosisResult
              result={diagnosisResult}
              onDownloadReport={handleDownloadReport}
              onShare={handleShare}
            />

            <div className="text-center">
              <button
                onClick={resetAnalysis}
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                تحليل صورة جديدة
              </button>
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Eye className="w-6 h-6 text-blue-600" />
              <span className="text-lg font-semibold text-gray-800">Aox Eye</span>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              تحليل ذكي للعين باستخدام الذكاء الاصطناعي
            </p>
            <div className="text-xs text-gray-500">
              <p className="mb-2">
                <strong>إخلاء مسؤولية:</strong> هذا التطبيق يقدم تشخيصاً أولياً للاستشارة فقط ولا يغني عن زيارة الطبيب المختص.
              </p>
              <p>© 2024 Aox Eye. جميع الحقوق محفوظة.</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
