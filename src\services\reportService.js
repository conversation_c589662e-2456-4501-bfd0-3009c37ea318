import jsPDF from 'jspdf';

// إعداد الخط العربي (يحتاج إلى ملف خط عربي)
const setupArabicFont = (doc) => {
  // في التطبيق الحقيقي، ستحتاج إلى تحميل خط عربي
  // doc.addFont('path/to/arabic-font.ttf', 'Arabic', 'normal');
  // doc.setFont('Arabic');
};

export const generatePDFReport = async (diagnosisResult, imageFile) => {
  try {
    const doc = new jsPDF();
    
    // إعداد الخط العربي
    setupArabicFont(doc);
    
    // إعداد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 20;
    let yPosition = margin;
    
    // Header
    doc.setFontSize(20);
    doc.setTextColor(37, 99, 235); // Blue color
    doc.text('Aox Eye - تقرير تحليل العين', pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 20;
    
    // Date and time
    doc.setFontSize(12);
    doc.setTextColor(100, 100, 100);
    const currentDate = new Date().toLocaleString('ar-EG');
    doc.text(`تاريخ التحليل: ${currentDate}`, pageWidth - margin, yPosition, { align: 'right' });
    yPosition += 20;
    
    // Diagnosis section
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('نتيجة التشخيص:', pageWidth - margin, yPosition, { align: 'right' });
    yPosition += 15;
    
    doc.setFontSize(14);
    doc.setTextColor(37, 99, 235);
    doc.text(diagnosisResult.condition, pageWidth - margin, yPosition, { align: 'right' });
    yPosition += 15;
    
    // Description
    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    const description = diagnosisResult.description;
    const splitDescription = doc.splitTextToSize(description, pageWidth - 2 * margin);
    doc.text(splitDescription, pageWidth - margin, yPosition, { align: 'right' });
    yPosition += splitDescription.length * 7 + 10;
    
    // Confidence
    doc.text(`دقة التشخيص: ${diagnosisResult.confidence}%`, pageWidth - margin, yPosition, { align: 'right' });
    yPosition += 20;
    
    // Treatment recommendations
    if (diagnosisResult.treatment && diagnosisResult.treatment.length > 0) {
      doc.setFontSize(14);
      doc.setTextColor(37, 99, 235);
      doc.text('التوصيات العلاجية:', pageWidth - margin, yPosition, { align: 'right' });
      yPosition += 15;
      
      doc.setFontSize(12);
      doc.setTextColor(0, 0, 0);
      diagnosisResult.treatment.forEach((treatment, index) => {
        const treatmentText = `${index + 1}. ${treatment}`;
        const splitTreatment = doc.splitTextToSize(treatmentText, pageWidth - 2 * margin);
        doc.text(splitTreatment, pageWidth - margin, yPosition, { align: 'right' });
        yPosition += splitTreatment.length * 7 + 5;
      });
      yPosition += 10;
    }
    
    // Warning if needed
    if (diagnosisResult.needsDoctor) {
      doc.setFillColor(254, 226, 226); // Light red background
      doc.rect(margin, yPosition - 5, pageWidth - 2 * margin, 25, 'F');
      
      doc.setFontSize(12);
      doc.setTextColor(185, 28, 28); // Red text
      doc.text('تحذير: يُنصح بزيارة طبيب العيون للحصول على تشخيص دقيق', 
               pageWidth - margin, yPosition + 10, { align: 'right' });
      yPosition += 35;
    }
    
    // Footer
    yPosition = pageHeight - 40;
    doc.setFontSize(10);
    doc.setTextColor(100, 100, 100);
    doc.text('إخلاء مسؤولية: هذا التشخيص أولي وللاستشارة فقط. لا يغني عن زيارة الطبيب المختص.',
             pageWidth / 2, yPosition, { align: 'center', maxWidth: pageWidth - 2 * margin });
    
    doc.text('Aox Eye - Powered by AI', pageWidth / 2, yPosition + 15, { align: 'center' });
    
    // Save the PDF
    const fileName = `Aox_Eye_Report_${new Date().getTime()}.pdf`;
    doc.save(fileName);
    
    return fileName;
    
  } catch (error) {
    console.error('خطأ في إنشاء التقرير:', error);
    throw new Error('حدث خطأ أثناء إنشاء التقرير');
  }
};

export const shareResult = async (diagnosisResult) => {
  try {
    const shareText = `
🔍 نتيجة تحليل العين - Aox Eye

📋 التشخيص: ${diagnosisResult.condition}
📊 دقة التشخيص: ${diagnosisResult.confidence}%
📅 التاريخ: ${new Date().toLocaleString('ar-EG')}

${diagnosisResult.needsDoctor ? '⚠️ يُنصح بزيارة طبيب العيون' : '✅ حالة مستقرة'}

🔗 تم التحليل باستخدام Aox Eye
    `.trim();
    
    if (navigator.share) {
      // استخدام Web Share API إذا كان متاحاً
      await navigator.share({
        title: 'نتيجة تحليل العين - Aox Eye',
        text: shareText,
        url: window.location.href
      });
    } else {
      // نسخ النص إلى الحافظة
      await navigator.clipboard.writeText(shareText);
      alert('تم نسخ النتيجة إلى الحافظة');
    }
    
  } catch (error) {
    console.error('خطأ في المشاركة:', error);
    
    // Fallback: نسخ النص إلى الحافظة
    try {
      const shareText = `نتيجة تحليل العين: ${diagnosisResult.condition} - دقة التشخيص: ${diagnosisResult.confidence}%`;
      await navigator.clipboard.writeText(shareText);
      alert('تم نسخ النتيجة إلى الحافظة');
    } catch (clipboardError) {
      alert('لا يمكن مشاركة النتيجة في الوقت الحالي');
    }
  }
};
